# 滴答清单 API 接口

本云函数封装了滴答清单(TickTick/Dida365)官方 Open API 的接口，提供对任务的增删查改功能。

## 功能特性

- 获取任务列表（支持按项目筛选、日期范围筛选和分页）
- 根据项目 ID 和任务 ID 获取特定任务
- 创建新任务
- 更新任务信息
- 完成/取消完成任务
- 删除任务
- 获取项目列表

## 使用前提

在使用此 API 接口前，您需要:

1. 安装 axios 依赖（已在 package.json 中声明）

> 注意：token 已在云函数内部配置，调用 API 时无需提供

## 快速开始

### 安装依赖

```bash
# 在云函数目录下运行
npm install
```

### 基础使用示例

```javascript
// 获取任务列表
const tasksResult = await uniCloud.callFunction({
  name: 'dida-todo',
  data: {
    method: 'getTasks',
    params: {
      limit: 50,
      skip: 0,
      completed: false,
    },
  },
})

// 根据项目ID和任务ID获取特定任务
const taskResult = await uniCloud.callFunction({
  name: 'dida-todo',
  data: {
    method: 'getTaskById',
    params: {
      projectId: 'PROJECT_ID',
      taskId: 'TASK_ID',
    },
  },
})

// 创建任务
const createResult = await uniCloud.callFunction({
  name: 'dida-todo',
  data: {
    method: 'createTask',
    params: {
      taskData: {
        title: '测试任务',
        content: '这是一个测试任务',
        priority: 1,
        startDate: '2023-06-10',
        dueDate: '2023-06-15',
        isAllDay: false,
      },
    },
  },
})

// 完成任务
const completeResult = await uniCloud.callFunction({
  name: 'dida-todo',
  data: {
    method: 'completeTask',
    params: {
      taskId: 'TASK_ID',
    },
  },
})
```

## API 说明

### getTasks(params)

获取任务列表，支持按项目筛选、日期范围筛选和分页。

- `params.projectId`: 项目 ID（可选）
- `params.startDate`: 开始日期，格式为 YYYY-MM-DD（可选）
- `params.endDate`: 结束日期，格式为 YYYY-MM-DD（可选）
- `params.completed`: 是否获取已完成的任务，默认为 false（可选）
- `params.limit`: 返回任务数量限制，默认为 50，最大为 100（可选）
- `params.skip`: 跳过任务数量，用于分页，默认为 0（可选）

### getTaskById(params)

根据项目 ID 和任务 ID 获取特定任务的详细信息。

**接口路径**: `GET /open/v1/project/{projectId}/task/{taskId}`

**参数说明**:

- `params.projectId`: 项目标识符（必须），字符串类型
- `params.taskId`: 任务标识符（必须），字符串类型

**响应状态码**:

- `200`: 成功，返回 Task 对象
- `401`: 未授权
- `403`: 禁止访问
- `404`: 未找到

**返回数据**: 包含任务详细信息的 JSON 对象，包括任务基本信息、时间相关字段、任务状态和优先级、提醒设置、子任务列表等。

### createTask(params)

创建一个新任务。

- `params.taskData`: 任务数据对象（必须），其中 title 字段为必填

### updateTask(params)

更新一个已存在的任务。

- `params.taskId`: 任务 ID（必须）
- `params.taskData`: 任务更新数据对象（必须）

### completeTask(params)

标记任务为已完成。

- `params.taskId`: 任务 ID（必须）

### uncompleteTask(params)

取消任务的已完成标记。

- `params.taskId`: 任务 ID（必须）

### deleteTask(params)

删除一个任务。

- `params.taskId`: 任务 ID（必须）

### getProjects()

获取项目列表。不需要参数。

## 任务数据结构

滴答清单的任务数据结构示例：

```json
{
  "id": "6555d9c3xxxxxxxx7e2183037",
  "projectId": "inboxxxxxxx718",
  "title": "示例任务",
  "content": "任务内容",
  "desc": "",
  "startDate": "2023-06-10",
  "dueDate": "2023-06-15",
  "timeZone": "Asia/Shanghai",
  "isAllDay": false,
  "reminders": [
    {
      "id": "6555d9c3xxxxxx183038",
      "trigger": "TRIGGER:-PT5M"
    }
  ],
  "priority": 0, // 0-3, 数字越大优先级越高
  "status": 0, // 0-未完成, 1-已完成
  "items": [],
  "progress": 0,
  "tags": []
}
```

## 注意事项

- 请合理使用 API，避免频繁请求导致触发限流
- 日期格式使用 YYYY-MM-DD（如 2023-06-10）
- 请参考滴答清单官方 API 文档：https://developer.dida365.com/docs#/openapi

## 错误处理

所有 API 返回的数据格式如下：

```json
// 成功
{
  "success": true,
  "data": { ... } // API返回的数据
}

// 失败
{
  "errCode": "错误代码",
  "errMsg": "错误信息",
  "error": {} // 可选，原始错误对象
}
```

请在调用 API 时进行适当的错误处理。
