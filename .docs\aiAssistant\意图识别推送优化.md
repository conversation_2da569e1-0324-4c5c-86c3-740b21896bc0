# AI 助手意图识别推送优化

## 背景

目前 AI 助手在处理用户消息时，会直接将 AI 的响应内容推送给前端显示。然而，AI 的响应中包含了结构化的意图识别信息（「意图类型」和「意图内容」），这些信息需要被提取并分别处理，以便前端可以根据不同的意图类型执行不同的操作。优化推送机制可以实现更精确的用户意图识别和处理。

## 需求

### 功能需求

1. 修改 AI 响应内容的处理逻辑，不再直接将原始内容推送给前端
2. 识别并提取 AI 返回内容中的「意图类型」信息
3. 识别并提取 AI 返回内容中的「意图内容」信息
4. 将提取出的意图类型和内容以结构化方式推送给前端
5. 前端根据不同意图类型进行相应的 UI 处理和功能响应

### 非功能需求

1. 保持流式响应的实时性，不显著增加处理延迟
2. 确保提取逻辑的准确性，正确识别格式化的意图信息
3. 健壮处理异常情况，例如 AI 返回的格式不符合预期

## 技术方案

### 后端实现（uniCloud-aliyun/cloudfunctions/ai/index.obj.js）

1. 在 chatStreamSSE 函数中修改流式数据处理逻辑：

   - 维护一个累积内容的变量，用于拼接流式返回的文本
   - 使用正则表达式识别「意图类型」模式，提取意图代码
   - 使用正则表达式识别「意图内容」模式，提取实际内容
   - 创建新的消息结构，包含意图类型和处理后的内容
   - 向前端推送结构化的消息

2. 处理流程图：

```mermaid
sequenceDiagram
    participant 前端
    participant 后端
    participant AI服务

    前端->>后端: 发送用户消息
    后端->>AI服务: 调用AI接口

    AI服务-->>后端: 流式返回内容(chunk1)
    Note over 后端: 累积内容，检测意图类型

    AI服务-->>后端: 流式返回内容(chunk2...)
    Note over 后端: 累积内容，持续检测

    后端-->>前端: 推送意图类型(如检测到)

    AI服务-->>后端: 流式返回内容(chunkN)
    Note over 后端: 检测意图内容

    后端-->>前端: 推送处理后的意图内容

    AI服务-->>后端: 流式返回完成
    后端-->>前端: 推送完成消息
```

### 前端实现（src/pages/aiAssistant/index.vue）

1. 修改 handleStreamMessage 函数，增加对新消息格式的处理：

   - 识别包含意图类型的消息，并据此更新 UI 状态
   - 根据不同的意图类型（create_task, find_task, chat）采取不同的 UI 反馈
   - 处理意图内容的显示方式

2. AI 消息状态展示优化：
   - 接收到 type = start 时，显示"意图识别中"的状态提示
   - 接收到意图类型（intent_type）后，根据不同类型显示对应状态（如"创建任务中"、"查询任务中"等）
   - 接收到意图内容（intent_content_start）时，隐藏状态提示，开始流式显示实际内容

### 具体代码实现思路

#### 后端处理逻辑：

```javascript
// 处理流式数据并实时推送
let fullContent = ''
let chunkCount = 0
let intentType = null
let isIntentContentStarted = false
let intentContent = ''

// 意图类型和内容的正则表达式
const intentTypeRegex = /「意图类型」：(\S+)/
const intentContentRegex = /「意图内容」：([\s\S]*)/

for await (const chunk of streamResponse) {
  const content = chunk.choices[0]?.delta?.content || ''
  if (content) {
    fullContent += content
    chunkCount++

    // 检测意图类型
    if (!intentType) {
      const typeMatch = intentTypeRegex.exec(fullContent)
      if (typeMatch) {
        intentType = typeMatch[1]
        await sseChannel.write({
          type: 'intent_type',
          intentType: intentType,
          timestamp: Date.now(),
        })
      }
    }

    // 检测意图内容
    if (intentType && !isIntentContentStarted) {
      const contentMatch = intentContentRegex.exec(fullContent)
      if (contentMatch) {
        isIntentContentStarted = true
        intentContent = contentMatch[1]
        await sseChannel.write({
          type: 'intent_content_start',
          content: intentContent,
          timestamp: Date.now(),
        })
      }
    } else if (isIntentContentStarted) {
      // 继续推送意图内容的后续部分
      await sseChannel.write({
        type: 'intent_content_chunk',
        content: content,
        timestamp: Date.now(),
      })
      intentContent += content
    }
  }
}
```

#### 前端处理逻辑：

```javascript
// 处理流式消息接收
const handleStreamMessage = (data) => {
  console.log('收到流式消息：', data)
  const loadingIndex = messages.value.findIndex((m) => m.loading)

  switch (data.type) {
    case 'start':
      // 处理开始消息，显示"意图识别中"状态
      if (loadingIndex !== -1) {
        messages.value.splice(loadingIndex, 1)
      }
      const startMessage = {
        _id: Date.now().toString(),
        content: '意图识别中...',
        type: 'text',
        isUser: false,
        streaming: true,
        statusMessage: true,
        time: new Date().toISOString(),
      }
      messages.value.push(startMessage)
      currentStreamingMessageId.value = startMessage._id
      isStreaming.value = true
      break

    case 'intent_type':
      // 处理意图类型，更新状态为对应的处理中状态
      if (currentStreamingMessageId.value) {
        const messageIndex = messages.value.findIndex((msg) => msg._id === currentStreamingMessageId.value)
        if (messageIndex !== -1) {
          messages.value[messageIndex].intentType = data.intentType

          // 根据不同意图类型更新状态提示
          switch (data.intentType) {
            case 'create_task':
              messages.value[messageIndex].content = '创建任务中...'
              break
            case 'find_task':
              messages.value[messageIndex].content = '查询任务中...'
              break
            case 'chat':
              messages.value[messageIndex].content = '思考回复中...'
              break
            default:
              messages.value[messageIndex].content = '处理中...'
          }
        }
      }
      break

    case 'intent_content_start':
      // 接收到内容开始，隐藏状态提示，显示实际内容
      if (currentStreamingMessageId.value) {
        const messageIndex = messages.value.findIndex((msg) => msg._id === currentStreamingMessageId.value)
        if (messageIndex !== -1) {
          // 移除状态标记，开始显示真实内容
          messages.value[messageIndex].statusMessage = false
          messages.value[messageIndex].content = data.content
        }
      }
      break

    case 'intent_content_chunk':
      // 处理内容块，累加显示
      if (currentStreamingMessageId.value) {
        const messageIndex = messages.value.findIndex((msg) => msg._id === currentStreamingMessageId.value)
        if (messageIndex !== -1) {
          messages.value[messageIndex].content += data.content
        }
      }
      break

    // 其他类型处理...
  }
}
```

## 风险评估

### 假设与未知因素

1. 假设 AI 模型始终返回格式一致的「意图类型」和「意图内容」
2. 假设网络传输稳定，不会导致流式数据丢失或乱序
3. 未知 AI 响应时间可能较长，影响用户体验

### 潜在风险

1. AI 返回的格式可能不符合预期，导致意图提取失败
   - 解决方案：添加健壮的错误处理，当无法提取意图时降级为普通消息处理
2. 正则表达式可能匹配不精确，误识别内容

   - 解决方案：确保正则表达式具有足够的特异性，并进行充分测试

3. 流式数据处理逻辑复杂化，可能增加延迟
   - 解决方案：优化代码逻辑，减少不必要的计算，保持处理的实时性
