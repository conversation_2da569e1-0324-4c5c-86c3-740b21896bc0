# AI 助理功能需求文档

## 1. 背景

当前，在我们的应用中管理任务、备忘等信息通常需要用户在不同的页面之间跳转和手动操作，流程相对繁琐，影响使用效率。为了提升用户体验，我们计划在 `@/pages/aiAssistant` 聊天页面中引入一个智能 AI 助理。用户可以通过自然语言与助理对话，快速完成任务管理、信息查询等操作，从而简化工作流程，提升交互的便捷性和智能化水平。

## 2. 需求

### 2.1 功能性需求 (当前阶段)

在当前开发阶段，我们将集中实现以下核心功能：

-   **添加任务 (`addTask`)**：
    -   用户可以通过自然语言快速创建任务。
    -   **示例**：“帮我添加一个任务，明天下午三点要参加产品会议。”
    -   **实现**：AI 需解析出“任务内容”（产品会议）和“截止时间”（明天下午三点），并调用接口创建任务。

-   **查询任务 (`queryTask`)**：
    -   用户可以查询不同时间维度或状态的任务。
    -   **示例**：“我今天有哪些任务？”或“查一下所有未完成的任务。”
    -   **实现**：AI 根据指令筛选并以清晰的列表形式（如卡片）返回结果。

-   **闲聊 (`chat`)**：
    -   对于不属于“添加任务”和“查询任务”的其他所有指令，都将归类为通用闲聊处理。
    -   **示例**：“今天天气怎么样？”或“讲个笑话吧。”
    -   **实现**：调用通用闲聊 API，返回自然语言回复。

### 2.2 非功能性需求

-   **响应速度**：核心指令的端到端响应时间应在 2 秒以内。
-   **理解准确率**：对于核心任务管理功能的意图理解准确率应高于 95%。
-   **交互友好**：对于模糊指令或操作失败，应提供友好的引导和清晰的错误提示。
-   **隐私安全**：用户的对话数据，特别是涉及内部知识库的内容，必须得到妥善保护，不得外泄。

## 3. 技术方案

### 3.1 实现思路

我们将采用“前端意图捕获 + 后端大模型解析 + API 执行”的模式。

1.  **前端**：在 `@/pages/aiAssistant/index.vue` 的聊天界面捕获用户输入的文本。
2.  **后端 (大模型)**：将文本发送至后端的大语言模型 (LLM) 服务。该服务负责解析文本，识别用户的**意图 (Intent)**。在当前阶段，意图仅限于 `addTask`、`queryTask` 和 `chat`。
3.  **API 调用**：
    - 如果意图是 `addTask` 或 `queryTask`，大模型服务将提取相关的**实体 (Entities)**（如任务名称、时间等），然后调用应用中已有的业务 API（如 `api/task.ts`）来执行操作。
    - 如果意图是 `chat`，则直接返回大模型的对话结果。
4.  **结果渲染**：前端接收到 API 的执行结果，并将其以文本、卡片或其他形式渲染在聊天界面上。

### 3.2 架构设计

```mermaid
graph TD
    A[用户在聊天页面输入] --> B[前端 UI]
    B --> C{AI 大模型服务 (LLM)}
    C --> D{意图识别与实体提取}
    D -- "意图: addTask" --> E[任务管理 API - 添加]
    D -- "意图: queryTask" --> F[任务管理 API - 查询]
    D -- "意图: chat" --> G[通用闲聊]
    E --> H[数据库]
    F --> H
    subgraph 响应生成
        E --> I
        F --> I
        G --> I
    end
    I[格式化回复] --> B
    B --> J[渲染 AI 回复和 UI 组件]
```

### 3.3 技术选型

-   **NLU 服务**：使用**大语言模型 (LLM)** 进行意图识别和实体提取，替换传统的 NLU 服务。这能提供更强的泛化能力和上下文理解能力。
-   **前端组件**：对于任务列表等复杂信息的展示，需要设计或复用专门的卡片式组件，以提供比纯文本更丰富的交互。

## 4. 风险评估

-   **大模型的幻觉和准确性**：
    -   **风险**：大模型可能会错误地解析意图或实体，或者产生“幻觉”，执行非预期的操作。
    -   **应对策略**：在调用任务 API 前，通过严格的规则或二次确认来校验大模型提取出的参数。例如，对于创建任务，可以向用户确认：“您是要创建任务‘xxx’吗？”。持续收集 bad case，通过 Prompt Engineering 进行优化。
-   **服务成本和延迟**：
    -   **风险**：大模型的调用成本通常较高，且响应可能存在延迟。
    -   **应对策略**：优化 Prompt，减少 Token 消耗。在前端做好加载状态管理，提升用户等待体验。考虑对高频、简单的查询进行缓存。
-   **数据隐私风险**：
    -   **风险**：将用户输入发送给第三方服务可能引发数据泄露。
    -   **应对策略**：与服务商签订严格的数据保密协议，并对敏感信息进行脱敏处理。在隐私政策中明确告知用户数据的使用方式。 