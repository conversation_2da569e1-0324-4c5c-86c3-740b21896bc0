# 每日任务进度贡献提示

## 1. 背景

当前，在“今日”页面中，当用户为一个关键结果（KR）添加进度后，系统仅通过一个短暂的toast提示告知操作成功，并在任务列表项上更新进度条。这种反馈机制较为孤立，用户无法直观地感受到本次操作对上层目标（Objective）当天整体进度的贡献。这削弱了用户的即时成就感，以及日常行为与长期目标之间的关联性，可能会影响用户的积极性和参与度。

为了强化这种关联，提供更具激励性的用户体验，我们建议设计一个专门的进度贡献提示功能。

## 2. 需求

### 2.1. 核心功能

在用户成功为任务添加进度后，在页面顶部显示一个醒目的、固定位置的提示卡片，清晰地展示本次进度对所属目标当天进度的贡献。

### 2.2. 组件设计

- **组件名称**: `z-progress-contribution-tip`
- **组件类型**: 全局通用组件
- **存放位置**: `src/components/z-progress-contribution-tip/`
- **设计要求**:
    - 组件在页面顶部固定显示，不随页面滚动而移动。
    - 视觉设计应醒目、简洁，能快速传递核心信息。
    - 组件应能接收并展示目标标题、目标当日总进度、当前完成进度以及本次新增的进度值。

### 2.3. 交互流程

1.  **触发**: 在“今日”页面，当用户通过“更新进度弹窗”成功提交一个任务的进度后，此提示卡片立即从页面顶部滑出显示。
2.  **内容展示**:
    - **目标标题**: 清晰展示该任务所属的目标（Objective）的标题。
    - **目标当日进度**: 以“已完成/总目标”的形式展示。
        - **总目标**: 当天，该目标下所有需要执行的任务的 `dailyTarget` 之和。
        - **已完成**: 在本次进度增加前，该目标下所有任务在当天已完成的进度之和。
    - **新增进度**: 突出显示本次操作增加的进度值（例如 `+5 公斤`）。
3.  **动画效果**:
    - **入场**: 提示卡片从页面顶部平滑滑入。
    - **进度增加动画**: 卡片显示后，新增的进度值以动画形式汇入“已完成”的总进度中，进度条和数值同时动态更新。这个动画是核心，必须清晰地表现出“贡献”的过程。
    - **退场**: 动画完成后，卡片停留2-3秒，然后自动向上平滑滑出并消失。

### 2.4. 技术实现要点

- 在 `today.vue` 的 `handleProgressUpdateSuccess` 方法中触发该组件。
- 在触发时，需要实时计算并传递以下数据给组件：
    - 所属目标的ID和标题。
    - 目标下所有今日任务的当日应完成总进度。
    - 目标下所有今日任务在更新前的当日已完成总进度。
    - 本次更新的进度值。
- 组件内部负责管理自身的显示/隐藏状态、出入场动画以及进度增加的动画效果。

## 3. 技术方案

### 3.1. 实现思路

在 `today.vue` 页面中，当 `handleProgressUpdateSuccess` 事件触发后，除了刷新当前的任务列表，还将执行以下操作：
1. 根据当前更新的任务ID (`currentTaskId`)，从任务列表 (`tasks.value`) 中找到该任务及其所属的 Objective 信息。
2. 遍历 `tasks.value`，筛选出与该 Objective 关联的所有今日任务。
3. 计算该 Objective 的“当日总目标”和“当日已完成进度（更新前）”。
4. 调用 `z-progress-contribution-tip` 组件，将计算好的数据和本次新增的进度值作为 props 传入。
5. 组件内部根据传入的 props，播放进度增加动画，并在短暂延迟后自动隐藏。

### 3.2. 架构设计

```mermaid
sequenceDiagram
    participant User as 用户
    participant today.vue as 今日页面
    participant UpdateProgressModal as 更新进度弹窗
    participant ProgressTip as 进度贡献提示

    User->>UpdateProgressModal: 输入进度并提交
    UpdateProgressModal-->>today.vue: 触发 save-success 事件 (含新增进度值)
    today.vue->>today.vue: 执行 handleProgressUpdateSuccess
    note right of today.vue: 1. 强制刷新任务列表<br/>2. 计算目标当日总进度<br/>3. 计算更新前已完成进度
    today.vue->>ProgressTip: 传递目标及进度数据，激活显示
    ProgressTip-->>User: 从顶部滑入，显示初始状态
    note right of ProgressTip: 播放“+5”动画，并将其汇入总进度
    ProgressTip-->>User: 进度条和数字动画更新
    loop 自动计时
        Note over ProgressTip: 停留2-3秒
    end
    ProgressTip-->>User: 向上滑出并消失
```

## 4. 风险评估

- **性能风险**: 在每次进度更新后都需要遍历任务列表进行计算，如果当日任务数量巨大，可能会有轻微延迟。
  - **应对策略**: 计算是在前端内存中完成，且仅限于单个目标下的任务，数据量有限，预计性能影响可忽略不计。
- **用户体验风险**: 提示动画或停留时间不当可能对用户造成干扰。
  - **应对策略**: 动画效果和停留时间将进行精细调整和测试，确保流畅、自然且信息传达清晰。
- **数据准确性风险**: 实时计算逻辑需要确保准确无误，特别是对于重复任务和不同单位的任务。
  - **应对策略**: 编写详细的单元测试来验证进度计算逻辑的准确性。 