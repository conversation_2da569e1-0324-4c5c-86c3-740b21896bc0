# “复述故事”功能增强需求文档

## 1. 背景
当前“复述故事”功能（`retell-story-page.vue`）为用户提供了基础的“生成素材->跟读复述->AI评价”的训练闭环。为了进一步提升用户粘性、满足个性化训练需求，并增强功能的专业性和趣味性，现计划对该页面进行功能增强。核心方向是为用户提供更丰富的素材选择权、更精细的评价反馈和更有趣的激励机制。

## 2. 需求
### 2.1 功能需求

#### 2.1.1 故事生成自定义
在“生成复述素材”按钮上方，新增一个“生成设置”区域，允许用户在生成故事前进行参数配置。
- **故事类型**：提供多种类型选项（如：生活日常、职场故事、科幻奇遇、历史趣闻、寓言故事）。
- **故事长度**：提供选项调节故事篇幅（如：短篇-约150字、中篇-约300字、长篇-约500字）。
- **难度等级**：提供难度选项（如：入门、中级、挑战），影响故事的词汇复杂度和句式结构。
- **自定义主题**：允许用户输入一个或多个关键词，作为故事生成的核心主题。

#### 2.1.2 复述过程辅助
- **关键词高亮**：在“故事原文”中，将系统提取的关键词进行高亮或加粗显示，方便用户在阅读时抓住重点。
- **疑难词/长难句翻译**：提供“点击翻译”功能。用户在阅读原文时，可以长按或点击某个单词或句子，系统会弹出翻译和释义。

#### 2.1.3 AI 评价与反馈增强
- **文本对比视图 (Diff View)**：在 AI 反馈卡片中，增加一个“文本对比”功能。该视图会并排展示“故事原文”和“用户复述文本”，并高亮显示二者之间的差异（如增、删、改），让用户一目了然地看到自己复述的准确性。
- **多维度评分**：在现有的评价指标基础上，增加更多维度的评分，如：
    - **发音准确度**：（需API支持）对用户的录音进行整体发音质量评分。
    - **关键词覆盖率**：计算用户复述内容中包含了多少原文的关键词。

#### 2.1.4 内容与记录管理
- **收藏故事**：用户可以收藏喜欢或认为经典的故事素材，方便后续反复练习。在“训练记录”弹窗中增加“我的收藏”分类。
- **使用自定义素材**：允许用户不通过 AI 生成，而是自己粘贴一段文本作为“故事原文”进行复述训练。

#### 2.1.5 趣味性与激励
- **成就徽章**：设立成就系统。当用户达成某些里程碑时（如：累计完成10个故事、首次尝试“挑战”难度、完成所有类型的故事等），系统会授予其相应的成就徽章。

### 2.2 非功能需求
- **性能**：文本对比（Diff）功能在客户端进行，需确保在长文本下依然保持流畅，不造成页面卡顿。
- **响应速度**：调用故事生成接口时，需明确告知用户预计等待时间，并提供稳定的加载动画。
- **UI/UX**：新增的“生成设置”区域默认应为收起状态，避免界面过于臃肿。用户点击后展开进行设置。

## 3. 技术方案
### 3.1 实现思路
1.  **UI层面**：在 `retell-story-page.vue` 的模板中，增加“生成设置”的折叠面板。使用 `uni-data-checkbox` 或类似组件实现类型、长度、难度的选择。
2.  **API层面**：
    - 扩展 `generateRetellConetnt` 接口，增加 `genre`, `length`, `difficulty`, `topic` 等参数。
    - 扩展 `evaluateRetell` 接口，使其返回更丰富的评价维度数据。
3.  **状态管理**：在 `script setup` 中使用 `ref` 定义新的配置项状态，并将其作为参数传递给接口。
4.  **文本对比**：引入轻量级的文本差异比对库（如 `diff-match-patch`），封装成一个独立的Vue组件 `DiffViewer.vue`，用于渲染对比结果。
5.  **收藏功能**：
    - 可在 `chatRecord` 数据库表中增加一个 `is_favorite` 字段（布尔类型）。
    - 提供一个 `setFavoriteApi` 接口，用于切换收藏状态。

### 3.2 架构设计
```mermaid
graph TD
    subgraph 用户界面 (retell-story-page.vue)
        A[生成设置面板] -- 传递配置 --> B(生成按钮);
        C[故事原文区域] -- 长按/点击 --> D[翻译服务];
        E[AI反馈卡片] -- 展示 --> F[文本对比视图];
    end
    
    subgraph API服务 (api/speak.ts)
        B --携带参数--> G[generateRetellConetnt API];
        H[用户复述] --提交--> I[evaluateRetell API];
    end

    subgraph 前端服务 (utils/services)
        D --> J[翻译JS模块];
        F -- 调用 --> K[文本Diff比对模块];
    end
    
    subgraph 后端服务 (uniCloud)
        G --> L[故事生成云函数];
        I --> M[智能评价云函数];
    end

    L -- 返回故事+关键词 --> C;
    M -- 返回多维度评价 --> E;
```

### 3.3 技术栈与约束
- **前端框架**: Vue 3 / uni-app
- **文本对比库**: `diff-match-patch` 或其他轻量级JS库
- **UI组件库**: uni-ui
- **性能约束**: 文本对比操作在500字以内，响应时间应小于100ms。

## 4. 风险评估
- **接口依赖**：故事生成和评价功能的增强强依赖后端接口的升级，需要前后端同步开发。
- **UI复杂度**：新增功能较多，可能导致页面信息过载。需要精心设计UI，确保用户操作流程清晰简洁。
- **性能瓶颈**：客户端的文本对比功能可能会在低端设备上遇到性能问题，需要进行充分测试和优化。 