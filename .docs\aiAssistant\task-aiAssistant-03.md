# 任务：[后端] 植入意图识别能力

## 1. 任务描述
修改 `uniCloud-aliyun/cloudfunctions/ai/index.obj.js` 的 `chat` 方法，在发送给大模型的 `system` 提示词中，加入意图识别的指令，使其能够根据用户输入判断意图并按指定JSON格式返回结果。

## 2. 所属功能模块
- AI 助理 - 后端

## 3. 技术实现详情
1.  **设计意图识别提示词 (Prompt)**:
    -   创建一个新的 `system` 提示词，指导大模型扮演一个能够识别意图的AI助手。
    -   提示词需要明确指出当前只支持 `addTask` 和 `queryTask` 两种意图。
    -   任何不属于这两种意图的输入，都应被归类为 `chat` 意图。
2.  **定义输出的 JSON 结构**:
    -   要求大模型必须以一个特定的JSON格式返回结果，以便后端解析。
    -   **示例**：
        ```json
        {
          "intent": "addTask",
          "entities": {
            "task_name": "参加产品会议",
            "time": "明天下午三点"
          },
          "response": "好的，已为您添加任务：明天下午三点参加产品会议。"
        }
        ```
        或者
        ```json
        {
          "intent": "chat",
          "response": "我只是一个AI模型，不太了解天气呢。"
        }
        ```
3.  **修改 `chat` 方法**:
    -   使用新的 `system` 提示词来调用大模型。
    -   解析大模型返回的JSON字符串。如果解析失败（例如大模型没有按要求返回JSON），则进行兜底处理，将整个返回内容作为 `chat` 类型的回复。

## 4. 验收标准
-   向 `/ai/chat` 接口发送“帮我添加一个任务...”，后端返回的 `data.type` 应为 `addTask`，且 `data.content` 包含解析出的实体。
-   向 `/ai/chat` 接口发送“今天天气怎么样”，后端返回的 `data.type` 应为 `chat`。
-   接口能够稳定处理大模型返回的JSON字符串和普通字符串。

## 5. 依赖关系
- `task-aiAssistant-01`: 依赖基础聊天功能的完成。

## 6. 优先级
- 中

## 7. 状态追踪
- [ ] 待办
- [ ] 进行中
- [ ] 已完成
- [ ] 已取消 