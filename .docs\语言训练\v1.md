# 关键词故事功能增强需求

## 1. 背景

`关键词讲故事`是`语言训练`模块的核心功能之一，旨在通过提供关键词来激发用户的创意和口语表达能力。当前版本功能较为单一，仅支持随机生成关键词，缺乏针对性和系统性的训练模式，难以满足用户多样化的练习需求（如备考、专业术语巩固等）。为了提升功能的实用性和趣味性，吸引用户进行更持久、更高效的刻意练习，现提出功能增强方案。

## 2. 需求

### 2.1 关键词系统增强

#### 2.1.1 新增“内置词库”模式

- **需求描述：** 在现有“随机生成”模式基础上，新增“内置词库”模式。用户可以选择不同领域或场景的专业词库进行训练。
- **功能细节：**
    - 提供一个词库选择器，以下拉菜单或标签页形式展示不同分类。
    - **初步分类规划：**
        - **考试相关：** 雅思、托福
        - **专业领域：** 商务、科技、医疗
        - **生活场景：** 旅行、社交、面试
    - 选择一个分类后，系统从中随机抽取 3-5 个词作为本轮训练的关键词。
- **验收标准：**
    - 用户可以在页面上看到词库分类并能成功选择。
    - 选择分类后，能展示对应领域的关键词。
    - 系统能正确记录当前选择的词库模式。

#### 2.1.2 新增“用户自定义”模式

- **需求描述：** 允许用户自行输入一组关键词进行训练。
- **功能细节：**
    - 提供一个文本输入区域，让用户可以输入 3-8 个关键词，以逗号或换行符分隔。
    - 系统需要对输入内容进行校验，确保关键词数量和格式符合要求。
- **验收标准：**
    - 用户可以找到输入框并成功提交自定义关键词。
    - 系统能正确解析用户输入的关键词列表。
    - 输入不符合规范时，应有明确的错误提示。

### 2.2 引入新训练模式

#### 2.2.1 新增“计时挑战”模式

- **需求描述：** 增加一种带有时间限制的训练模式。
- **功能细节：**
    - 用户开始录音后，屏幕上出现倒计时（可设置为 60 秒/90 秒/120 秒）。
    - 时间结束后，录音自动停止并提交。
- **验收标准：**
    - 模式选择界面有“计时挑战”选项。
    - 录音开始后，倒计时正确显示并运行。
    - 时间耗尽后，能自动完成录音和提交过程。

### 2.3 优化 AI 反馈与数据追踪

#### 2.3.1 新增“关键词高亮”功能

- **需求描述：** 在 AI 生成的文字稿中，高亮显示用户成功使用的关键词。
- **功能细节：**
    - 对比用户故事的文字稿和本轮的关键词列表。
    - 将文字稿中匹配到的关键词用特殊样式（如不同颜色或背景色）标出。
- **验收标准：**
    - 语音转写完成后，文字稿中用到的关键词被成功高亮。

#### 2.3.2 新增“个人训练档案”页面

- **需求描述：** 创建一个新页面，用于展示用户的长期训练数据和进步趋势。
- **技术方案：**
    - **前端实现：**
        - 使用图表库（如 ECharts）进行数据可视化。
        - 新建`src/pages/speak/training-archive.vue`页面。
    - **数据存储：**
        - 每次训练结束后，将评价结果（流畅度、关键词使用率等核心指标）及训练日期存入数据库。
        - 需扩展`chatRecord`表结构或新建一个`trainingLog`表来存储这些结构化数据。
- **可视化图表规划：**
    - **训练日历：** 以热力图或打点方式展示用户的训练频率。
    - **能力雷达图：** 综合展示用户在流畅度、创意、语法等多个维度的平均得分。
    - **进步曲线图：** 按周或月为单位，展示核心指标（如平均分）的变化趋势。
- **验收标准：**
    - 用户可以从某个入口进入“个人训练档案”页面。
    - 页面上的图表能正确加载并展示历史训练数据。

## 3. 技术方案

### 3.1 前端实现思路

- **组件化改造：**
    - **关键词模块 (`l-keywords-panel.vue`):** 将关键词的生成、选择、展示逻辑封装成一个独立的局部组件。该组件负责处理不同模式（随机、内置、自定义）下的关键词状态管理，并通过事件向父组件（`keyword-story-page.vue`）派发关键词列表。
    - **模式选择模块 (`l-mode-selector.vue`):** 将训练模式（经典、计时挑战）的选择功能封装成一个组件。
- **状态管理：**
    - 使用 Pinia 或 Vuex 来管理训练的全局状态，如当前的关键词来源模式、训练模式、用户信息等，方便跨组件共享状态。
- **UI 设计:**
    - 在`keyword-story-page.vue`页面顶部或关键词区域增加模式切换的 UI（如 Segmented Control）。
    - 自定义关键词功能可以放在一个弹窗（Popup）中，或者在关键词面板内提供一个输入框。
    - 个人训练档案的入口可以放在`我的`页面，或者在关键词故事页面的导航栏右上角增加一个档案图标按钮。

### 3.2 架构设计

```mermaid
graph TD
    subgraph "keyword-story-page.vue"
        A[模式选择器 l-mode-selector] --> C{当前训练模式};
        B[关键词面板 l-keywords-panel] --> D{当前关键词};
        C & D --> E[故事录入/转写];
        E --> F[提交AI评价];
    end

    subgraph "后端服务"
        G[AI评价引擎]
        H[数据库]
    end

    subgraph "新功能"
        B -- 内置/自定义 --> I[词库管理];
        F --> J[训练数据记录];
        J --> H;
        K[个人训练档案页面] -- 从 --> H;
    end

    F --> G;
    G --> F;

    style K fill:#d4fcd7,stroke:#333,stroke-width:2px
    style J fill:#d4fcd7,stroke:#333,stroke-width:2px
    style I fill:#d4fcd7,stroke:#333,stroke-width:2px
```

### 3.3 技术栈与约束

- **前端框架：** Vue 3
- **UI 库：** uni-app
- **图表库：** `qiun-data-charts` (项目已有) 或引入 ECharts for uniapp
- **状态管理：** Pinia

## 4. 风险评估

- **数据模型设计：** 需要谨慎设计训练记录的数据库表结构，确保其可扩展性，以便未来增加更多的分析维度。
- **用户体验：** 新增多种模式和选项后，页面布局会变得更复杂。需要精心设计 UI，避免信息过载，保持简洁易用的操作流程。
- **性能：** 个人训练档案页面加载大量历史数据和图表时，可能会有性能问题。需要考虑数据分页加载或后端聚合等优化手段。 