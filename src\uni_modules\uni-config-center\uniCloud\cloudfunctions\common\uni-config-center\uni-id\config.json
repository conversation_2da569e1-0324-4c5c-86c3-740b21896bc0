{"passwordSecret": "zcjihkjsajhdgweuyhioenwiewwesd", "tokenSecret": "zcjudiunwdvtefhidsjicbdwo", "tokenExpiresIn": 2592000, "tokenExpiresThreshold": 600, "passwordErrorLimit": 6, "bindTokenToDevice": false, "passwordErrorRetryTime": 3600, "autoSetInviteCode": false, "forceInviteCode": false, "preferedAppPlatform": "app", "app": {"tokenExpiresIn": 2592000, "oauth": {"weixin": {"appid": "填写来源微信开放平台https://open.weixin.qq.com/创建的应用的appid", "appsecret": "填写来源微信开放平台https://open.weixin.qq.com/创建的应用的appsecret"}, "apple": {"bundleId": "苹果开发者后台获取的bundleId"}}}, "web": {"oauth": {"h5-weixin": {"appid": "", "appsecret": ""}, "web-weixin": {"appid": "手机微信扫码登录，所用的微信开放平台（https://open.weixin.qq.com/）-网站应用的appid", "appsecret": "微信开放平台-网站应用的appsecret"}}}, "mp-weixin": {"oauth": {"weixin": {"appid": "wxaefb27fcd23a9593", "appsecret": "d33034b290c9ce4e916fdc18187986fa"}}}, "mp-alipay": {"oauth": {"alipay": {"appid": "支付宝小程序登录用到的appid、privateKey请参考支付宝小程序的文档进行设置或者获取，https://opendocs.alipay.com/open/291/105971#LDsXr", "privateKey": "支付宝小程序登录用到的appid、privateKey请参考支付宝小程序的文档进行设置或者获取，https://opendocs.alipay.com/open/291/105971#LDsXr"}}}, "service": {"sms": {"name": "应用名称，对应短信模版的name", "codeExpiresIn": 300, "smsKey": "短信密钥key，开通短信服务处可以看到", "smsSecret": "短信密钥secret，开通短信服务处可以看到"}, "univerify": {"appid": "当前应用的appid，使用云函数URL化，此项必须配置", "apiKey": "apiKey 和 apiSecret 在开发者中心获取，开发者中心：https://dev.dcloud.net.cn/uniLogin/index?type=0，文档：https://ask.dcloud.net.cn/article/37965", "apiSecret": ""}}}