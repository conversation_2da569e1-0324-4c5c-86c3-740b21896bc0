# 任务：[后端] 实现基础聊天功能与规范响应结构

## 1. 任务描述
在云函数 `uniCloud-aliyun/cloudfunctions/ai/index.obj.js` 的 `chat` 方法中，实现与大模型的基础对话能力，并定义一套标准的API响应结构，以便前端进行统一处理。

## 2. 所属功能模块
- AI 助理 - 后端

## 3. 技术实现详情
1.  **完善 `chat` 方法**:
    -   此方法应复用 `speak` 方法中与 OpenAI (DeepSeek) API 的交互逻辑。
    -   接收前端传递的 `messages` (历史对话记录) 和 `message` (当前消息) 参数。
2.  **定义标准响应结构**:
    -   无论成功或失败，API 都应返回一个统一的 JSON 对象。
    -   **成功响应**:
        ```json
        {
          "errCode": 0,
          "errMsg": "success",
          "data": {
            "type": "chat",
            "content": "这是AI的回复内容"
          }
        }
        ```
    -   **失败响应**:
        ```json
        {
          "errCode": "API_ERROR",
          "errMsg": "调用AI接口失败"
        }
        ```
    -   `data.type` 字段用于未来区分不同的意图（如 `addTask`, `queryTask`）。在此任务中，其值固定为 `chat`。

## 4. 验收标准
-   调用 `/ai/chat` 接口，传递合法的消息体，应能收到来自大模型的聊天回复，且响应格式符合上述规范。
-   当不传递 `apiKey` 或传递错误参数时，接口应返回格式正确的失败响应。

## 5. 依赖关系
- 无

## 6. 优先级
- 高

## 7. 状态追踪
- [ ] 待办
- [ ] 进行中
- [ ] 已完成
- [ ] 已取消 