# 任务：[前端] 对接聊天接口，完成基础对话流程

## 1. 任务描述
在 `src/pages/aiAssistant/index.vue` 中，修改 `handleSendMessage` 方法，调用真实的 `/ai/chat` 接口，并根据接口返回的数据更新聊天记录。

## 2. 所属功能模块
- AI 助理 - 前端

## 3. 技术实现详情
1.  **引入 API 调用**:
    -   使用 request 来请求 `/ai/chat` 接口。
2.  **修改 `handleSendMessage` 方法**:
    -   在发送用户消息后，立即调用后端接口。
    -   传递参数：`message` (当前用户输入) 和 `messages` (历史聊天记录，需要进行格式转换，移除不必要的字段，仅保留 `role` 和 `content`)。
    -   在接收到后端响应前，显示加载状态。
3.  **处理接口响应**:
    -   移除之前的模拟 AI 回复 `setTimeout` 逻辑。
    -   在收到响应后，移除加载状态。
    -   **成功时**：将后端返回的 `data.content` 作为 AI 回复内容，添加到 `messages` 数组中。
    -   **失败时**：在聊天界面显示一条错误提示消息，内容为后端的 `errMsg`。

## 4. 验收标准
-   在聊天界面输入消息并发送，能够看到真实的 AI 回复。
-   在网络异常或后端返回错误时，界面上应显示相应的错误提示。
-   加载状态能够正确显示和隐藏。

## 5. 依赖关系
- `task-aiAssistant-01`: 依赖后端基础聊天功能的完成。

## 6. 优先级
- 高

## 7. 状态追踪
- [ ] 待办
- [ ] 进行中
- [ ] 已完成
- [ ] 已取消 