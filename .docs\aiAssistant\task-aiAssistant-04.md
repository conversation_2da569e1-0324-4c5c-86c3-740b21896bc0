# 任务：[前端] 解析并处理意图识别结果

## 1. 任务描述
在 `src/pages/aiAssistant/index.vue` 中，扩展对 `/ai/chat` 接口响应的处理能力。前端需要能够解析后端返回的结构化数据，并根据不同的 `type` (意图) 做出不同的UI响应。

## 2. 所属功能模块
- AI 助理 - 前端

## 3. 技术实现详情
1.  **修改响应处理逻辑**:
    -   在 `handleSendMessage` 的成功回调中，检查后端返回的 `data.type` 字段。
2.  **处理 `chat` 意图**:
    -   如果 `data.type` 是 `chat`，则直接将 `data.content` 或 `data.response` 作为文本消息展示，与 `task-aiAssistant-02` 的实现保持一致。
3.  **处理 `addTask` 意图**:
    -   如果 `data.type` 是 `addTask`，前端可以采用更丰富的形式来展示结果。
    -   例如，可以显示一个确认消息（如 `data.response`），并附加一个结构化的任务卡片，清晰地展示已添加的任务内容和时间。
    -   这可能需要创建一个新的UI组件 `l-task-card.vue` 用于展示任务信息。
4.  **处理 `queryTask` 意图**:
    -   如果 `data.type` 是 `queryTask`，前端应将 `data.content`（预期是一个任务数组）渲染成一个任务列表。
    -   可以复用 `l-task-card.vue` 组件来展示列表中的每一个任务。

## 4. 验收标准
-   当用户进行普通闲聊时，前端能正确展示AI的文本回复。
-   当用户发送“添加任务”指令后，前端能在聊天流中展示一个确认信息或一个结构化的任务卡片。
-   当用户发送“查询任务”指令后，前端能在聊天流中展示一个任务列表。

## 5. 依赖关系
- `task-aiAssistant-02`: 依赖前端基础对话流程的完成。
- `task-aiAssistant-03`: 依赖后端意图识别功能的实现。

## 6. 优先级
- 中

## 7. 状态追踪
- [ ] 待办
- [ ] 进行中
- [ ] 已完成
- [ ] 已取消 